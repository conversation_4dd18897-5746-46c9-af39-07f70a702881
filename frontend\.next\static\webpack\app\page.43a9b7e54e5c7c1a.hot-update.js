"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Disconnected\");\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const vadDisposeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    useEffect({\n        \"Home.useEffect\": ()=>{\n            return ({\n                \"Home.useEffect\": ()=>{\n                    if (socketRef.current) {\n                        socketRef.current.close();\n                    }\n                    if (vadDisposeRef.current) {\n                        vadDisposeRef.current();\n                    }\n                    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                        mediaRecorderRef.current.stop();\n                    }\n                    if (streamRef.current) {\n                        streamRef.current.getTracks().forEach({\n                            \"Home.useEffect\": (track)=>track.stop()\n                        }[\"Home.useEffect\"]);\n                    }\n                }\n            })[\"Home.useEffect\"];\n        }\n    }[\"Home.useEffect\"], []);\n    // Function to update audio level visualization\n    const updateAudioLevel = (audioData)=>{\n        // Calculate audio level from the audio data\n        let sum = 0;\n        for(let i = 0; i < audioData.length; i++){\n            sum += Math.abs(audioData[i]);\n        }\n        const avg = sum / audioData.length;\n        // Normalize and amplify for better visualization\n        const level = Math.min(1, avg * 5);\n        setAudioLevel(level);\n        // Log audio levels periodically to help debug\n        if (Math.random() < 0.01) {\n            console.log(\"Current audio level:\", level.toFixed(3));\n        }\n    };\n    const handleToggleRecording = ()=>{\n        if (isRecording) {\n            // Stop recording\n            console.log(\"Stopping recording...\");\n            // Stop VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Stop MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Stop media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n            // Close WebSocket\n            if (socketRef.current) {\n                socketRef.current.close();\n            }\n            setIsRecording(false);\n            setAudioLevel(0);\n        } else {\n            // Start recording\n            console.log(\"Starting recording...\");\n            setMessages([]);\n            connectSocketAndStartRecording();\n        }\n    };\n    const connectSocketAndStartRecording = function() {\n        let isReconnect = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {\n            console.log(\"WebSocket already connected, starting recording.\");\n            startRecording();\n            return;\n        }\n        setConnectionStatus(\"Connecting...\");\n        console.log(\"Attempting to connect WebSocket...\");\n        const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n        socketRef.current = socket;\n        socket.onopen = ()=>{\n            setConnectionStatus(\"Connected\");\n            console.log(\"WebSocket connection established.\");\n            // Only start recording if it's not a reconnect action\n            if (!isReconnect) {\n                startRecording();\n            }\n        };\n        socket.onmessage = async (event)=>{\n            console.log(\"WebSocket message received:\", event.data);\n            if (typeof event.data === \"string\") {\n                try {\n                    const message = JSON.parse(event.data);\n                    setMessages((prev)=>[\n                            ...prev,\n                            message\n                        ]);\n                } catch (error) {\n                    console.error(\"Error parsing JSON message:\", error);\n                }\n            } else if (event.data instanceof Blob) {\n                // Handle incoming MP3 audio data\n                console.log(\"Received audio blob, size:\", event.data.size);\n                try {\n                    // Create MP3 blob with proper MIME type\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        // Stop any currently playing audio\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        // Set new audio source and play\n                        audioPlayerRef.current.src = audioUrl;\n                        // Add event listeners for better error handling\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            // Clean up the object URL when playback ends\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        // Load the audio\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 audio data:\", error);\n                }\n            } else if (event.data instanceof ArrayBuffer) {\n                // Handle ArrayBuffer MP3 data\n                console.log(\"Received audio ArrayBuffer, size:\", event.data.byteLength);\n                try {\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio from ArrayBuffer loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio from ArrayBuffer:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 ArrayBuffer data:\", error);\n                }\n            } else {\n                console.warn(\"Received unknown data type:\", typeof event.data);\n            }\n        };\n        socket.onclose = ()=>{\n            setConnectionStatus(\"Disconnected\");\n            setIsRecording(false);\n            setAudioLevel(0);\n            setIsPlayingAudio(false);\n            console.log(\"WebSocket connection closed.\");\n            // Clean up VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Clean up MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Clean up media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n        };\n        socket.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n            setConnectionStatus(\"Error: Connection failed\");\n            setIsRecording(false);\n        };\n    };\n    const startRecording = async ()=>{\n        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {\n            console.error(\"Cannot start recording, WebSocket is not open.\");\n            setConnectionStatus(\"Error: Not connected\");\n            return;\n        }\n        try {\n            setIsRecording(true);\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: true,\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            streamRef.current = stream;\n            // Set up MediaRecorder with WAV format processing\n            // Use MediaRecorder to capture audio, then convert to WAV\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            // Process audio data when available\n            mediaRecorder.ondataavailable = async (event)=>{\n                if (event.data.size > 0) {\n                    try {\n                        var _socketRef_current;\n                        // Convert the blob to audio data\n                        const arrayBuffer = await event.data.arrayBuffer();\n                        const audioContext = new AudioContext({\n                            sampleRate: 16000\n                        });\n                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n                        // Get mono channel data (convert to mono if stereo)\n                        const channelData = audioBuffer.getChannelData(0);\n                        // Resample to 16kHz if needed\n                        const targetSampleRate = 16000;\n                        const resampledData = resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);\n                        // Convert to PCM 16-bit\n                        const pcmData = new Int16Array(resampledData.length);\n                        for(let i = 0; i < resampledData.length; i++){\n                            const sample = Math.max(-1, Math.min(1, resampledData[i]));\n                            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;\n                        }\n                        // Create WAV buffer\n                        const wavBuffer = createWAVBuffer(pcmData, targetSampleRate);\n                        if (((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.readyState) === WebSocket.OPEN) {\n                            console.log(\"Sending WAV audio chunk, size:\", wavBuffer.byteLength);\n                            socketRef.current.send(wavBuffer);\n                        }\n                        await audioContext.close();\n                    } catch (error) {\n                        console.error(\"Error processing audio data:\", error);\n                    }\n                }\n            };\n            // Start recording with small time slices\n            mediaRecorder.start(250); // 250ms chunks for real-time processing\n            // Set up VAD\n            const vadDispose = await recordAudio({\n                onSpeechStart: ()=>{\n                    console.log(\"Speech detected - starting to send audio\");\n                    setAudioLevel(0.8); // Visual feedback for speech detection\n                },\n                onSpeechEnd: ()=>{\n                    console.log(\"Speech ended\");\n                    setAudioLevel(0);\n                },\n                onSpeechAvailable: (speechData)=>{\n                    console.log(\"Speech available:\", speechData);\n                    // Update audio level based on speech data\n                    updateAudioLevel(speechData.audioData);\n                },\n                onSpeechOngoing: (speechData)=>{\n                    // Update audio level during ongoing speech\n                    updateAudioLevel(speechData.audioData);\n                }\n            });\n            vadDisposeRef.current = vadDispose;\n            console.log(\"Recording started with VAD and MediaRecorder\");\n        } catch (error) {\n            var _socketRef_current;\n            console.error(\"Error starting recording:\", error);\n            setConnectionStatus(\"Error: Could not access microphone\");\n            setIsRecording(false);\n            (_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.close();\n        }\n    };\n    const handleReconnect = ()=>{\n        console.log(\"Reconnect button clicked.\");\n        connectSocketAndStartRecording(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center text-gray-800 dark:text-white mb-4\",\n                    children: \"Real-time Audio Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleToggleRecording,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors \".concat(isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"),\n                                            disabled: connectionStatus === \"Connecting...\" || isRecording && connectionStatus !== \"Connected\",\n                                            children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this),\n                                        (connectionStatus === \"Disconnected\" || connectionStatus.startsWith(\"Error\")) && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Status: \",\n                                        connectionStatus\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Playing AI Response\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this),\n                                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Audio Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-green-500\",\n                                                style: {\n                                                    width: \"\".concat(Math.max(5, audioLevel * 100), \"%\"),\n                                                    transition: \"width 0.1s ease-out\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this),\n                                        audioLevel === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-500 mt-1\",\n                                            children: \"Warning: No audio detected! Check your microphone settings.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 space-y-4\",\n                            children: messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(msg.type === \"user_transcript\" ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end\" : \"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: msg.type === \"user_transcript\" ? \"You\" : \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.data\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioPlayerRef,\n                        className: \"w-full\",\n                        controls: true,\n                        controlsList: \"nodownload\",\n                        onPlay: ()=>setIsPlayingAudio(true),\n                        onPause: ()=>setIsPlayingAudio(false),\n                        onEnded: ()=>setIsPlayingAudio(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 383,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 382,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"aPMe9o8eTm97BtvtC9SeJK5vQc4=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});