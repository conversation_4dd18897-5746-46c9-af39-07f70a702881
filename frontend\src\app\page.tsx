"use client";

import { useState, useRef } from "react";

type ConversationEntry = {
  userText: string;
  agentReply: string;
};

export default function Home() {
  const [isRecording, setIsRecording] = useState(false);
  const [conversation, setConversation] = useState<ConversationEntry[]>([]);
  const [status, setStatus] = useState("Ready");
  const [isPlayingAudio, setIsPlayingAudio] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  const handleToggleRecording = async () => {
    if (isRecording) {
      // Stop recording
      setStatus("Stopping...");
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== "inactive"
      ) {
        mediaRecorderRef.current.stop();
      }
      setIsRecording(false);
    } else {
      // Start recording
      setStatus("Starting...");
      await startRecording();
    }
  };

  const startRecording = async () => {
    try {
      setStatus("Getting microphone access...");

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
        },
      });

      audioChunksRef.current = [];

      // Set up MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        setStatus("Processing audio...");

        // Create audio blob from chunks
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/webm",
        });

        // Send to backend via WebSocket
        await sendAudioToBackend(audioBlob);

        // Stop the stream
        stream.getTracks().forEach((track) => track.stop());
        setStatus("Ready");
      };

      // Start recording
      mediaRecorder.start();
      setIsRecording(true);
      setStatus("Recording... Click stop when done");
    } catch (error) {
      console.error("Error starting recording:", error);
      setStatus("Error: Could not access microphone");
    }
  };

  // Helper function to convert audio to WAV format
  const convertToWav = async (audioBlob: Blob): Promise<ArrayBuffer> => {
    const arrayBuffer = await audioBlob.arrayBuffer();
    const audioContext = new AudioContext({ sampleRate: 16000 });
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

    // Get mono channel data
    const channelData = audioBuffer.getChannelData(0);

    // Convert to 16-bit PCM
    const pcmData = new Int16Array(channelData.length);
    for (let i = 0; i < channelData.length; i++) {
      const sample = Math.max(-1, Math.min(1, channelData[i]));
      pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
    }

    // Create WAV buffer
    const wavBuffer = createWavBuffer(pcmData, 16000);
    await audioContext.close();
    return wavBuffer;
  };

  // Helper function to create WAV buffer
  const createWavBuffer = (
    pcmData: Int16Array,
    sampleRate: number
  ): ArrayBuffer => {
    const dataLength = pcmData.length * 2;
    const buffer = new ArrayBuffer(44 + dataLength);
    const view = new DataView(buffer);

    // WAV header
    view.setUint32(0, 0x52494646, false); // "RIFF"
    view.setUint32(4, 36 + dataLength, true); // File size - 8
    view.setUint32(8, 0x57415645, false); // "WAVE"
    view.setUint32(12, 0x666d7420, false); // "fmt "
    view.setUint32(16, 16, true); // Subchunk1Size
    view.setUint16(20, 1, true); // AudioFormat (PCM)
    view.setUint16(22, 1, true); // NumChannels (mono)
    view.setUint32(24, sampleRate, true); // SampleRate
    view.setUint32(28, sampleRate * 2, true); // ByteRate
    view.setUint16(32, 2, true); // BlockAlign
    view.setUint16(34, 16, true); // BitsPerSample
    view.setUint32(36, 0x64617461, false); // "data"
    view.setUint32(40, dataLength, true); // Subchunk2Size

    // Write PCM data
    let offset = 44;
    for (let i = 0; i < pcmData.length; i++) {
      view.setInt16(offset, pcmData[i], true);
      offset += 2;
    }

    return buffer;
  };

  const sendAudioToBackend = async (audioBlob: Blob) => {
    try {
      setStatus("Converting audio to WAV...");

      // Convert WebM to WAV format
      const wavBuffer = await convertToWav(audioBlob);

      setStatus("Connecting to server...");
      const socket = new WebSocket("ws://127.0.0.1:8000/api/agent/voice");

      socket.onopen = async () => {
        setStatus("Sending audio...");
        socket.send(wavBuffer);
      };

      socket.onmessage = async (event) => {
        if (typeof event.data === "string") {
          // Handle JSON response with transcription and AI reply
          try {
            const response = JSON.parse(event.data);
            if (response.user_text && response.agent_reply) {
              setConversation((prev) => [
                ...prev,
                {
                  userText: response.user_text,
                  agentReply: response.agent_reply,
                },
              ]);
              setStatus("Received response");
            } else if (response.error) {
              setStatus(`Error: ${response.error}`);
            }
          } catch (error) {
            console.error("Error parsing JSON:", error);
            setStatus("Error parsing response");
          }
        } else if (
          event.data instanceof Blob ||
          event.data instanceof ArrayBuffer
        ) {
          // Handle MP3 audio response
          setStatus("Playing AI response...");
          const audioData =
            event.data instanceof Blob
              ? event.data
              : new Blob([event.data], { type: "audio/mpeg" });
          const audioUrl = URL.createObjectURL(audioData);

          if (audioPlayerRef.current) {
            audioPlayerRef.current.src = audioUrl;
            audioPlayerRef.current.onended = () => {
              URL.revokeObjectURL(audioUrl);
              setIsPlayingAudio(false);
              setStatus("Ready");
            };
            audioPlayerRef.current.onplay = () => setIsPlayingAudio(true);
            audioPlayerRef.current.onpause = () => setIsPlayingAudio(false);

            try {
              await audioPlayerRef.current.play();
            } catch (error) {
              console.error("Error playing audio:", error);
              setStatus("Error playing audio");
            }
          }
        }
      };

      socket.onerror = (error) => {
        console.error("WebSocket error:", error);
        setStatus("Connection error");
      };

      socket.onclose = () => {
        setStatus("Ready");
      };
    } catch (error) {
      console.error("Error sending audio:", error);
      setStatus("Error sending audio");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-6">Voice Chat</h1>

        <div className="bg-white rounded-lg shadow p-6 mb-4">
          <div className="text-center mb-4">
            <button
              onClick={handleToggleRecording}
              className={`px-6 py-3 rounded-lg text-white font-semibold ${
                isRecording
                  ? "bg-red-500 hover:bg-red-600"
                  : "bg-blue-500 hover:bg-blue-600"
              }`}
              disabled={status.includes("Error")}
            >
              {isRecording ? "Stop Recording" : "Start Recording"}
            </button>
          </div>

          <p className="text-center text-sm text-gray-600 mb-4">
            Status: {status}
          </p>

          {isPlayingAudio && (
            <div className="text-center mb-4">
              <div className="inline-flex items-center space-x-2 text-blue-600">
                <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Playing AI Response</span>
              </div>
            </div>
          )}
        </div>

        {/* Conversation History */}
        <div className="bg-white rounded-lg shadow p-6 mb-4">
          <h2 className="text-xl font-semibold mb-4">Conversation</h2>
          <div className="space-y-4">
            {conversation.map((entry, index) => (
              <div key={index} className="border-b pb-4 last:border-b-0">
                <div className="mb-2">
                  <span className="font-semibold text-blue-600">You:</span>
                  <p className="text-gray-800">{entry.userText}</p>
                </div>
                <div>
                  <span className="font-semibold text-green-600">AI:</span>
                  <p className="text-gray-800">{entry.agentReply}</p>
                </div>
              </div>
            ))}
            {conversation.length === 0 && (
              <p className="text-gray-500 text-center">
                No conversation yet. Start recording to begin!
              </p>
            )}
          </div>
        </div>

        {/* Audio Player */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-2">Audio Player</h3>
          <audio
            ref={audioPlayerRef}
            className="w-full"
            controls
            controlsList="nodownload"
          />
        </div>
      </div>
    </div>
  );
}
