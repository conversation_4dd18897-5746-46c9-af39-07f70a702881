"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Ready\");\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function to update audio level visualization\n    const updateAudioLevel = (audioData)=>{\n        // Calculate audio level from the audio data\n        let sum = 0;\n        for(let i = 0; i < audioData.length; i++){\n            sum += Math.abs(audioData[i]);\n        }\n        const avg = sum / audioData.length;\n        // Normalize and amplify for better visualization\n        const level = Math.min(1, avg * 5);\n        setAudioLevel(level);\n        // Log audio levels periodically to help debug\n        if (Math.random() < 0.01) {\n            console.log(\"Current audio level:\", level.toFixed(3));\n        }\n    };\n    const handleToggleRecording = ()=>{\n        if (isRecording) {\n            // Stop recording\n            console.log(\"Stopping recording...\");\n            // Stop VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Stop MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Stop media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n            // Close WebSocket\n            if (socketRef.current) {\n                socketRef.current.close();\n            }\n            setIsRecording(false);\n            setAudioLevel(0);\n        } else {\n            // Start recording\n            console.log(\"Starting recording...\");\n            setMessages([]);\n            connectSocketAndStartRecording();\n        }\n    };\n    const connectSocketAndStartRecording = function() {\n        let isReconnect = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {\n            console.log(\"WebSocket already connected, starting recording.\");\n            startRecording();\n            return;\n        }\n        setConnectionStatus(\"Connecting...\");\n        console.log(\"Attempting to connect WebSocket...\");\n        const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n        socketRef.current = socket;\n        socket.onopen = ()=>{\n            setConnectionStatus(\"Connected\");\n            console.log(\"WebSocket connection established.\");\n            // Only start recording if it's not a reconnect action\n            if (!isReconnect) {\n                startRecording();\n            }\n        };\n        socket.onmessage = async (event)=>{\n            console.log(\"WebSocket message received:\", event.data);\n            if (typeof event.data === \"string\") {\n                try {\n                    const message = JSON.parse(event.data);\n                    setMessages((prev)=>[\n                            ...prev,\n                            message\n                        ]);\n                } catch (error) {\n                    console.error(\"Error parsing JSON message:\", error);\n                }\n            } else if (event.data instanceof Blob) {\n                // Handle incoming MP3 audio data\n                console.log(\"Received audio blob, size:\", event.data.size);\n                try {\n                    // Create MP3 blob with proper MIME type\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        // Stop any currently playing audio\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        // Set new audio source and play\n                        audioPlayerRef.current.src = audioUrl;\n                        // Add event listeners for better error handling\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            // Clean up the object URL when playback ends\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        // Load the audio\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 audio data:\", error);\n                }\n            } else if (event.data instanceof ArrayBuffer) {\n                // Handle ArrayBuffer MP3 data\n                console.log(\"Received audio ArrayBuffer, size:\", event.data.byteLength);\n                try {\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio from ArrayBuffer loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio from ArrayBuffer:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 ArrayBuffer data:\", error);\n                }\n            } else {\n                console.warn(\"Received unknown data type:\", typeof event.data);\n            }\n        };\n        socket.onclose = ()=>{\n            setConnectionStatus(\"Disconnected\");\n            setIsRecording(false);\n            setAudioLevel(0);\n            setIsPlayingAudio(false);\n            console.log(\"WebSocket connection closed.\");\n            // Clean up VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Clean up MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Clean up media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n        };\n        socket.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n            setConnectionStatus(\"Error: Connection failed\");\n            setIsRecording(false);\n        };\n    };\n    const startRecording = async ()=>{\n        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {\n            console.error(\"Cannot start recording, WebSocket is not open.\");\n            setConnectionStatus(\"Error: Not connected\");\n            return;\n        }\n        try {\n            setIsRecording(true);\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: true,\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            streamRef.current = stream;\n            // Set up MediaRecorder with WAV format processing\n            // Use MediaRecorder to capture audio, then convert to WAV\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            // Process audio data when available\n            mediaRecorder.ondataavailable = async (event)=>{\n                if (event.data.size > 0) {\n                    try {\n                        var _socketRef_current;\n                        // Convert the blob to audio data\n                        const arrayBuffer = await event.data.arrayBuffer();\n                        const audioContext = new AudioContext({\n                            sampleRate: 16000\n                        });\n                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n                        // Get mono channel data (convert to mono if stereo)\n                        const channelData = audioBuffer.getChannelData(0);\n                        // Resample to 16kHz if needed\n                        const targetSampleRate = 16000;\n                        const resampledData = resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);\n                        // Convert to PCM 16-bit\n                        const pcmData = new Int16Array(resampledData.length);\n                        for(let i = 0; i < resampledData.length; i++){\n                            const sample = Math.max(-1, Math.min(1, resampledData[i]));\n                            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;\n                        }\n                        // Create WAV buffer\n                        const wavBuffer = createWAVBuffer(pcmData, targetSampleRate);\n                        if (((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.readyState) === WebSocket.OPEN) {\n                            console.log(\"Sending WAV audio chunk, size:\", wavBuffer.byteLength);\n                            socketRef.current.send(wavBuffer);\n                        }\n                        await audioContext.close();\n                    } catch (error) {\n                        console.error(\"Error processing audio data:\", error);\n                    }\n                }\n            };\n            // Start recording with small time slices\n            mediaRecorder.start(250); // 250ms chunks for real-time processing\n            // Set up VAD\n            const vadDispose = await recordAudio({\n                onSpeechStart: ()=>{\n                    console.log(\"Speech detected - starting to send audio\");\n                    setAudioLevel(0.8); // Visual feedback for speech detection\n                },\n                onSpeechEnd: ()=>{\n                    console.log(\"Speech ended\");\n                    setAudioLevel(0);\n                },\n                onSpeechAvailable: (speechData)=>{\n                    console.log(\"Speech available:\", speechData);\n                    // Update audio level based on speech data\n                    updateAudioLevel(speechData.audioData);\n                },\n                onSpeechOngoing: (speechData)=>{\n                    // Update audio level during ongoing speech\n                    updateAudioLevel(speechData.audioData);\n                }\n            });\n            vadDisposeRef.current = vadDispose;\n            console.log(\"Recording started with VAD and MediaRecorder\");\n        } catch (error) {\n            var _socketRef_current;\n            console.error(\"Error starting recording:\", error);\n            setConnectionStatus(\"Error: Could not access microphone\");\n            setIsRecording(false);\n            (_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.close();\n        }\n    };\n    const handleReconnect = ()=>{\n        console.log(\"Reconnect button clicked.\");\n        connectSocketAndStartRecording(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center text-gray-800 dark:text-white mb-4\",\n                    children: \"Real-time Audio Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleToggleRecording,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors \".concat(isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"),\n                                            disabled: connectionStatus === \"Connecting...\" || isRecording && connectionStatus !== \"Connected\",\n                                            children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        (connectionStatus === \"Disconnected\" || connectionStatus.startsWith(\"Error\")) && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Status: \",\n                                        connectionStatus\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Playing AI Response\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this),\n                                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Audio Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-green-500\",\n                                                style: {\n                                                    width: \"\".concat(Math.max(5, audioLevel * 100), \"%\"),\n                                                    transition: \"width 0.1s ease-out\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        audioLevel === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-500 mt-1\",\n                                            children: \"Warning: No audio detected! Check your microphone settings.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 space-y-4\",\n                            children: messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(msg.type === \"user_transcript\" ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end\" : \"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: msg.type === \"user_transcript\" ? \"You\" : \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.data\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioPlayerRef,\n                        className: \"w-full\",\n                        controls: true,\n                        controlsList: \"nodownload\",\n                        onPlay: ()=>setIsPlayingAudio(true),\n                        onPause: ()=>setIsPlayingAudio(false),\n                        onEnded: ()=>setIsPlayingAudio(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 360,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 359,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"+Di68zq1cJ5C8sbpxejmM8uSD2U=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});