"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Ready\");\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleToggleRecording = async ()=>{\n        if (isRecording) {\n            // Stop recording\n            setStatus(\"Stopping...\");\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            setIsRecording(false);\n        } else {\n            // Start recording\n            setStatus(\"Starting...\");\n            await startRecording();\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setStatus(\"Getting microphone access...\");\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            audioChunksRef.current = [];\n            // Set up MediaRecorder\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                setStatus(\"Processing audio...\");\n                // Create audio blob from chunks\n                const audioBlob = new Blob(audioChunksRef.current, {\n                    type: \"audio/webm\"\n                });\n                // Send to backend via WebSocket\n                await sendAudioToBackend(audioBlob);\n                // Stop the stream\n                stream.getTracks().forEach((track)=>track.stop());\n                setStatus(\"Ready\");\n            };\n            // Start recording\n            mediaRecorder.start();\n            setIsRecording(true);\n            setStatus(\"Recording... Click stop when done\");\n        } catch (error) {\n            console.error(\"Error starting recording:\", error);\n            setStatus(\"Error: Could not access microphone\");\n        }\n    };\n    const sendAudioToBackend = async (audioBlob)=>{\n        try {\n            setStatus(\"Connecting to server...\");\n            const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n            socket.onopen = async ()=>{\n                setStatus(\"Sending audio...\");\n                // Convert blob to array buffer and send\n                const arrayBuffer = await audioBlob.arrayBuffer();\n                socket.send(arrayBuffer);\n            };\n            socket.onmessage = async (event)=>{\n                if (typeof event.data === \"string\") {\n                    // Handle JSON response with transcription and AI reply\n                    try {\n                        const response = JSON.parse(event.data);\n                        if (response.user_text && response.agent_reply) {\n                            setConversation((prev)=>[\n                                    ...prev,\n                                    {\n                                        userText: response.user_text,\n                                        agentReply: response.agent_reply\n                                    }\n                                ]);\n                            setStatus(\"Received response\");\n                        } else if (response.error) {\n                            setStatus(\"Error: \".concat(response.error));\n                        }\n                    } catch (error) {\n                        console.error(\"Error parsing JSON:\", error);\n                        setStatus(\"Error parsing response\");\n                    }\n                } else if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {\n                    // Handle MP3 audio response\n                    setStatus(\"Playing AI response...\");\n                    const audioData = event.data instanceof Blob ? event.data : new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioData);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onended = ()=>{\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                            setStatus(\"Ready\");\n                        };\n                        audioPlayerRef.current.onplay = ()=>setIsPlayingAudio(true);\n                        audioPlayerRef.current.onpause = ()=>setIsPlayingAudio(false);\n                        try {\n                            await audioPlayerRef.current.play();\n                        } catch (error) {\n                            console.error(\"Error playing audio:\", error);\n                            setStatus(\"Error playing audio\");\n                        }\n                    }\n                }\n            };\n            socket.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                setStatus(\"Connection error\");\n            };\n            socket.onclose = ()=>{\n                setStatus(\"Ready\");\n            };\n        } catch (error) {\n            console.error(\"Error sending audio:\", error);\n            setStatus(\"Error sending audio\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-center mb-6\",\n                    children: \"Voice Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleToggleRecording,\n                                className: \"px-6 py-3 rounded-lg text-white font-semibold \".concat(isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"),\n                                disabled: status.includes(\"Error\"),\n                                children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mb-4\",\n                            children: [\n                                \"Status: \",\n                                status\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this),\n                        isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-blue-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Playing AI Response\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Conversation\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                conversation.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-b pb-4 last:border-b-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-blue-600\",\n                                                        children: \"You:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-800\",\n                                                        children: entry.userText\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: \"AI:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-800\",\n                                                        children: entry.agentReply\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this)),\n                                conversation.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center\",\n                                    children: \"No conversation yet. Start recording to begin!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-2\",\n                            children: \"Audio Player\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                            ref: audioPlayerRef,\n                            className: \"w-full\",\n                            controls: true,\n                            controlsList: \"nodownload\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"+Di68zq1cJ5C8sbpxejmM8uSD2U=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});