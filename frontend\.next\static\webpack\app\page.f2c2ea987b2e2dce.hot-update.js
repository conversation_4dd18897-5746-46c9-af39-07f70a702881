"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Ready\");\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleToggleRecording = async ()=>{\n        if (isRecording) {\n            // Stop recording\n            setStatus(\"Stopping...\");\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            setIsRecording(false);\n        } else {\n            // Start recording\n            setStatus(\"Starting...\");\n            await startRecording();\n        }\n    };\n    const connectSocketAndStartRecording = function() {\n        let isReconnect = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {\n            console.log(\"WebSocket already connected, starting recording.\");\n            startRecording();\n            return;\n        }\n        setConnectionStatus(\"Connecting...\");\n        console.log(\"Attempting to connect WebSocket...\");\n        const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n        socketRef.current = socket;\n        socket.onopen = ()=>{\n            setConnectionStatus(\"Connected\");\n            console.log(\"WebSocket connection established.\");\n            // Only start recording if it's not a reconnect action\n            if (!isReconnect) {\n                startRecording();\n            }\n        };\n        socket.onmessage = async (event)=>{\n            console.log(\"WebSocket message received:\", event.data);\n            if (typeof event.data === \"string\") {\n                try {\n                    const message = JSON.parse(event.data);\n                    setMessages((prev)=>[\n                            ...prev,\n                            message\n                        ]);\n                } catch (error) {\n                    console.error(\"Error parsing JSON message:\", error);\n                }\n            } else if (event.data instanceof Blob) {\n                // Handle incoming MP3 audio data\n                console.log(\"Received audio blob, size:\", event.data.size);\n                try {\n                    // Create MP3 blob with proper MIME type\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        // Stop any currently playing audio\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        // Set new audio source and play\n                        audioPlayerRef.current.src = audioUrl;\n                        // Add event listeners for better error handling\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            // Clean up the object URL when playback ends\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        // Load the audio\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 audio data:\", error);\n                }\n            } else if (event.data instanceof ArrayBuffer) {\n                // Handle ArrayBuffer MP3 data\n                console.log(\"Received audio ArrayBuffer, size:\", event.data.byteLength);\n                try {\n                    const mp3Blob = new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(mp3Blob);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.pause();\n                        audioPlayerRef.current.currentTime = 0;\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onloadeddata = ()=>{\n                            var _audioPlayerRef_current;\n                            console.log(\"MP3 audio from ArrayBuffer loaded successfully\");\n                            setIsPlayingAudio(true);\n                            (_audioPlayerRef_current = audioPlayerRef.current) === null || _audioPlayerRef_current === void 0 ? void 0 : _audioPlayerRef_current.play().catch((error)=>{\n                                console.error(\"Error playing MP3 audio:\", error);\n                                setIsPlayingAudio(false);\n                            });\n                        };\n                        audioPlayerRef.current.onerror = (error)=>{\n                            console.error(\"Error loading MP3 audio from ArrayBuffer:\", error);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onended = ()=>{\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.onplay = ()=>{\n                            setIsPlayingAudio(true);\n                        };\n                        audioPlayerRef.current.onpause = ()=>{\n                            setIsPlayingAudio(false);\n                        };\n                        audioPlayerRef.current.load();\n                    }\n                } catch (error) {\n                    console.error(\"Error handling MP3 ArrayBuffer data:\", error);\n                }\n            } else {\n                console.warn(\"Received unknown data type:\", typeof event.data);\n            }\n        };\n        socket.onclose = ()=>{\n            setConnectionStatus(\"Disconnected\");\n            setIsRecording(false);\n            setAudioLevel(0);\n            setIsPlayingAudio(false);\n            console.log(\"WebSocket connection closed.\");\n            // Clean up VAD\n            if (vadDisposeRef.current) {\n                vadDisposeRef.current();\n                vadDisposeRef.current = null;\n            }\n            // Clean up MediaRecorder\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            // Clean up media stream\n            if (streamRef.current) {\n                streamRef.current.getTracks().forEach((track)=>track.stop());\n                streamRef.current = null;\n            }\n        };\n        socket.onerror = (error)=>{\n            console.error(\"WebSocket error:\", error);\n            setConnectionStatus(\"Error: Connection failed\");\n            setIsRecording(false);\n        };\n    };\n    const startRecording = async ()=>{\n        if (!socketRef.current || socketRef.current.readyState !== WebSocket.OPEN) {\n            console.error(\"Cannot start recording, WebSocket is not open.\");\n            setConnectionStatus(\"Error: Not connected\");\n            return;\n        }\n        try {\n            setIsRecording(true);\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: false,\n                    noiseSuppression: false,\n                    autoGainControl: true,\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            streamRef.current = stream;\n            // Set up MediaRecorder with WAV format processing\n            // Use MediaRecorder to capture audio, then convert to WAV\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            // Process audio data when available\n            mediaRecorder.ondataavailable = async (event)=>{\n                if (event.data.size > 0) {\n                    try {\n                        var _socketRef_current;\n                        // Convert the blob to audio data\n                        const arrayBuffer = await event.data.arrayBuffer();\n                        const audioContext = new AudioContext({\n                            sampleRate: 16000\n                        });\n                        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);\n                        // Get mono channel data (convert to mono if stereo)\n                        const channelData = audioBuffer.getChannelData(0);\n                        // Resample to 16kHz if needed\n                        const targetSampleRate = 16000;\n                        const resampledData = resampleAudio(channelData, audioBuffer.sampleRate, targetSampleRate);\n                        // Convert to PCM 16-bit\n                        const pcmData = new Int16Array(resampledData.length);\n                        for(let i = 0; i < resampledData.length; i++){\n                            const sample = Math.max(-1, Math.min(1, resampledData[i]));\n                            pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;\n                        }\n                        // Create WAV buffer\n                        const wavBuffer = createWAVBuffer(pcmData, targetSampleRate);\n                        if (((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.readyState) === WebSocket.OPEN) {\n                            console.log(\"Sending WAV audio chunk, size:\", wavBuffer.byteLength);\n                            socketRef.current.send(wavBuffer);\n                        }\n                        await audioContext.close();\n                    } catch (error) {\n                        console.error(\"Error processing audio data:\", error);\n                    }\n                }\n            };\n            // Start recording with small time slices\n            mediaRecorder.start(250); // 250ms chunks for real-time processing\n            // Set up VAD\n            const vadDispose = await recordAudio({\n                onSpeechStart: ()=>{\n                    console.log(\"Speech detected - starting to send audio\");\n                    setAudioLevel(0.8); // Visual feedback for speech detection\n                },\n                onSpeechEnd: ()=>{\n                    console.log(\"Speech ended\");\n                    setAudioLevel(0);\n                },\n                onSpeechAvailable: (speechData)=>{\n                    console.log(\"Speech available:\", speechData);\n                    // Update audio level based on speech data\n                    updateAudioLevel(speechData.audioData);\n                },\n                onSpeechOngoing: (speechData)=>{\n                    // Update audio level during ongoing speech\n                    updateAudioLevel(speechData.audioData);\n                }\n            });\n            vadDisposeRef.current = vadDispose;\n            console.log(\"Recording started with VAD and MediaRecorder\");\n        } catch (error) {\n            var _socketRef_current;\n            console.error(\"Error starting recording:\", error);\n            setConnectionStatus(\"Error: Could not access microphone\");\n            setIsRecording(false);\n            (_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.close();\n        }\n    };\n    const handleReconnect = ()=>{\n        console.log(\"Reconnect button clicked.\");\n        connectSocketAndStartRecording(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center text-gray-800 dark:text-white mb-4\",\n                    children: \"Real-time Audio Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleToggleRecording,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors \".concat(isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"),\n                                            disabled: connectionStatus === \"Connecting...\" || isRecording && connectionStatus !== \"Connected\",\n                                            children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this),\n                                        (connectionStatus === \"Disconnected\" || connectionStatus.startsWith(\"Error\")) && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Status: \",\n                                        connectionStatus\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Playing AI Response\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Audio Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-green-500\",\n                                                style: {\n                                                    width: \"\".concat(Math.max(5, audioLevel * 100), \"%\"),\n                                                    transition: \"width 0.1s ease-out\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this),\n                                        audioLevel === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-500 mt-1\",\n                                            children: \"Warning: No audio detected! Check your microphone settings.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 space-y-4\",\n                            children: messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(msg.type === \"user_transcript\" ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end\" : \"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: msg.type === \"user_transcript\" ? \"You\" : \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.data\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioPlayerRef,\n                        className: \"w-full\",\n                        controls: true,\n                        controlsList: \"nodownload\",\n                        onPlay: ()=>setIsPlayingAudio(true),\n                        onPause: ()=>setIsPlayingAudio(false),\n                        onEnded: ()=>setIsPlayingAudio(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 319,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"+Di68zq1cJ5C8sbpxejmM8uSD2U=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});