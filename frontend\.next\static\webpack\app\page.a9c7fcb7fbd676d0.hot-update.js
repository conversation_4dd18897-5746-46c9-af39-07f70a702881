"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Home() {\n    _s();\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [conversation, setConversation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Ready\");\n    const [isPlayingAudio, setIsPlayingAudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const audioPlayerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleToggleRecording = async ()=>{\n        if (isRecording) {\n            // Stop recording\n            setStatus(\"Stopping...\");\n            if (mediaRecorderRef.current && mediaRecorderRef.current.state !== \"inactive\") {\n                mediaRecorderRef.current.stop();\n            }\n            setIsRecording(false);\n        } else {\n            // Start recording\n            setStatus(\"Starting...\");\n            await startRecording();\n        }\n    };\n    const startRecording = async ()=>{\n        try {\n            setStatus(\"Getting microphone access...\");\n            // Get user media\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    channelCount: 1,\n                    sampleRate: 16000\n                }\n            });\n            audioChunksRef.current = [];\n            // Set up MediaRecorder\n            const mediaRecorder = new MediaRecorder(stream, {\n                mimeType: \"audio/webm;codecs=opus\"\n            });\n            mediaRecorderRef.current = mediaRecorder;\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            mediaRecorder.onstop = async ()=>{\n                setStatus(\"Processing audio...\");\n                // Create audio blob from chunks\n                const audioBlob = new Blob(audioChunksRef.current, {\n                    type: \"audio/webm\"\n                });\n                // Send to backend via WebSocket\n                await sendAudioToBackend(audioBlob);\n                // Stop the stream\n                stream.getTracks().forEach((track)=>track.stop());\n                setStatus(\"Ready\");\n            };\n            // Start recording\n            mediaRecorder.start();\n            setIsRecording(true);\n            setStatus(\"Recording... Click stop when done\");\n        } catch (error) {\n            console.error(\"Error starting recording:\", error);\n            setStatus(\"Error: Could not access microphone\");\n        }\n    };\n    const sendAudioToBackend = async (audioBlob)=>{\n        try {\n            setStatus(\"Connecting to server...\");\n            const socket = new WebSocket(\"ws://127.0.0.1:8000/api/agent/voice\");\n            socket.onopen = async ()=>{\n                setStatus(\"Sending audio...\");\n                // Convert blob to array buffer and send\n                const arrayBuffer = await audioBlob.arrayBuffer();\n                socket.send(arrayBuffer);\n            };\n            socket.onmessage = async (event)=>{\n                if (typeof event.data === \"string\") {\n                    // Handle JSON response with transcription and AI reply\n                    try {\n                        const response = JSON.parse(event.data);\n                        if (response.user_text && response.agent_reply) {\n                            setConversation((prev)=>[\n                                    ...prev,\n                                    {\n                                        userText: response.user_text,\n                                        agentReply: response.agent_reply\n                                    }\n                                ]);\n                            setStatus(\"Received response\");\n                        } else if (response.error) {\n                            setStatus(\"Error: \".concat(response.error));\n                        }\n                    } catch (error) {\n                        console.error(\"Error parsing JSON:\", error);\n                        setStatus(\"Error parsing response\");\n                    }\n                } else if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {\n                    // Handle MP3 audio response\n                    setStatus(\"Playing AI response...\");\n                    const audioData = event.data instanceof Blob ? event.data : new Blob([\n                        event.data\n                    ], {\n                        type: \"audio/mpeg\"\n                    });\n                    const audioUrl = URL.createObjectURL(audioData);\n                    if (audioPlayerRef.current) {\n                        audioPlayerRef.current.src = audioUrl;\n                        audioPlayerRef.current.onended = ()=>{\n                            URL.revokeObjectURL(audioUrl);\n                            setIsPlayingAudio(false);\n                            setStatus(\"Ready\");\n                        };\n                        audioPlayerRef.current.onplay = ()=>setIsPlayingAudio(true);\n                        audioPlayerRef.current.onpause = ()=>setIsPlayingAudio(false);\n                        try {\n                            await audioPlayerRef.current.play();\n                        } catch (error) {\n                            console.error(\"Error playing audio:\", error);\n                            setStatus(\"Error playing audio\");\n                        }\n                    }\n                }\n            };\n            socket.onerror = (error)=>{\n                console.error(\"WebSocket error:\", error);\n                setStatus(\"Connection error\");\n            };\n            socket.onclose = ()=>{\n                setStatus(\"Ready\");\n            };\n        } catch (error) {\n            console.error(\"Error sending audio:\", error);\n            setStatus(\"Error sending audio\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900 font-sans\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center text-gray-800 dark:text-white mb-4\",\n                    children: \"Real-time Audio Chat\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleToggleRecording,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors \".concat(isRecording ? \"bg-red-500 hover:bg-red-600\" : \"bg-blue-500 hover:bg-blue-600\"),\n                                            disabled: connectionStatus === \"Connecting...\" || isRecording && connectionStatus !== \"Connected\",\n                                            children: isRecording ? \"Stop Recording\" : \"Start Recording\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        (connectionStatus === \"Disconnected\" || connectionStatus.startsWith(\"Error\")) && !isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleReconnect,\n                                            className: \"px-6 py-3 rounded-full text-white font-semibold transition-colors bg-green-500 hover:bg-green-600\",\n                                            children: \"Reconnect\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-sm text-gray-600 dark:text-gray-400\",\n                                    children: [\n                                        \"Status: \",\n                                        connectionStatus\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                isPlayingAudio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse w-2 h-2 bg-blue-500 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Playing AI Response\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this),\n                                isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: \"Audio Level:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-4 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full bg-green-500\",\n                                                style: {\n                                                    width: \"\".concat(Math.max(5, audioLevel * 100), \"%\"),\n                                                    transition: \"width 0.1s ease-out\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        audioLevel === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-500 mt-1\",\n                                            children: \"Warning: No audio detected! Check your microphone settings.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 space-y-4\",\n                            children: messages.map((msg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 rounded-lg \".concat(msg.type === \"user_transcript\" ? \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 self-end\" : \"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 self-start\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold\",\n                                            children: msg.type === \"user_transcript\" ? \"You\" : \"AI\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: msg.data\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                        ref: audioPlayerRef,\n                        className: \"w-full\",\n                        controls: true,\n                        controlsList: \"nodownload\",\n                        onPlay: ()=>setIsPlayingAudio(true),\n                        onPause: ()=>setIsPlayingAudio(false),\n                        onEnded: ()=>setIsPlayingAudio(false)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\hackon_tts\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"+Di68zq1cJ5C8sbpxejmM8uSD2U=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});